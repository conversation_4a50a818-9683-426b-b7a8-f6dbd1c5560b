<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="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" />
      </map>
    </option>
  </component>
</project>