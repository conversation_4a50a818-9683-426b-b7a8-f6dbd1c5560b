{"Version": "1.2", "Data": {"Source": "h:\\p4\\dev\\baoli\\source\\baoli\\ai\\myaicontroller.cpp", "ProvidedModule": "", "PCH": "h:\\p4\\dev\\baoli\\intermediate\\build\\win64\\x64\\baolieditor\\development\\unrealed\\sharedpch.unrealed.project.valapi.cpp20.inclorderunreal5_3.h.pch", "Includes": ["h:\\p4\\dev\\baoli\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\baoli\\definitions.baoli.h", "h:\\p4\\dev\\baoli\\source\\baoli\\ai\\myaicontroller.h", "d:\\ue_5.5\\engine\\source\\runtime\\aimodule\\classes\\aicontroller.h", "d:\\ue_5.5\\engine\\source\\runtime\\aimodule\\classes\\aitypes.h", "d:\\ue_5.5\\engine\\source\\runtime\\navigationsystem\\public\\navigationsystemtypes.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\ai\\navigationsystemconfig.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\navigationsystemconfig.generated.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\aitypes.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\aimodule\\classes\\perception\\aiperceptionlistenerinterface.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\aiperceptionlistenerinterface.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\aimodule\\classes\\genericteamagentinterface.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\genericteamagentinterface.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\public\\visuallogger\\visualloggerdebugsnapshotinterface.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\visualloggerdebugsnapshotinterface.generated.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\aicontroller.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\aimodule\\classes\\navigation\\pathfollowingcomponent.h", "d:\\ue_5.5\\engine\\source\\runtime\\navigationsystem\\public\\navigationdata.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\ai\\navigation\\navigationdatainterface.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\navigationdatainterface.generated.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\navigationsystem\\uht\\navigationdata.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\aimodule\\classes\\airesourceinterface.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\airesourceinterface.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\gameframework\\navmovementcomponent.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\gameframework\\navmovementinterface.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\navmovementinterface.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\ai\\navigation\\pathfollowingagentinterface.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\pathfollowingagentinterface.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\gameframework\\movementcomponent.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\movementcomponent.generated.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\navmovementcomponent.generated.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\pathfollowingcomponent.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\aimodule\\classes\\perception\\aiperceptioncomponent.h", "d:\\ue_5.5\\engine\\source\\runtime\\aimodule\\classes\\perception\\aiperceptiontypes.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\aiperceptiontypes.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\aimodule\\classes\\perception\\aisense.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\aisense.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\aimodule\\classes\\perception\\aiperceptionsystem.h", "d:\\ue_5.5\\engine\\source\\runtime\\aimodule\\classes\\aisubsystem.h", "d:\\ue_5.5\\engine\\source\\runtime\\aimodule\\classes\\aisystem.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\ai\\aisystembase.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\aisystembase.generated.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\aisystem.generated.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\aisubsystem.generated.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\aiperceptionsystem.generated.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\aiperceptioncomponent.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\aimodule\\classes\\perception\\aisenseconfig_sight.h", "d:\\ue_5.5\\engine\\source\\runtime\\aimodule\\classes\\perception\\aisenseconfig.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\aisenseconfig.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\aimodule\\classes\\perception\\aisense_sight.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\misc\\mttransactionallysafeaccessdetector.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\aisense_sight.generated.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\aisenseconfig_sight.generated.h", "h:\\p4\\dev\\baoli\\intermediate\\build\\win64\\unrealeditor\\inc\\baoli\\uht\\myaicontroller.generated.h", "h:\\p4\\dev\\baoli\\source\\baoli\\ai\\ai_characterbase.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\gameframework\\character.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\gameframework\\charactermovementreplication.h", "d:\\ue_5.5\\engine\\source\\runtime\\net\\core\\public\\net\\core\\nettoken\\nettokenexportcontext.h", "d:\\ue_5.5\\engine\\source\\runtime\\experimental\\iris\\core\\public\\iris\\serialization\\irisobjectreferencepackagemap.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\iriscore\\uht\\irisobjectreferencepackagemap.generated.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\charactermovementreplication.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\gameframework\\rootmotionsource.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\rootmotionsource.generated.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\character.generated.h", "h:\\p4\\dev\\baoli\\intermediate\\build\\win64\\unrealeditor\\inc\\baoli\\uht\\ai_characterbase.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\navigationsystem\\public\\navigationpath.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\navigationsystem\\uht\\navigationpath.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\navigationsystem\\public\\navigationsystem.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\ai\\navigation\\navigationdirtyelement.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\ai\\navigation\\navigationinvokerpriority.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\navigationinvokerpriority.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\ai\\navigationsystembase.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\navigationsystembase.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\navigationsystem\\public\\navigationoctree.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\ai\\navigation\\navigationelement.h", "d:\\ue_5.5\\engine\\source\\runtime\\navigationsystem\\public\\navigationoctreecontroller.h", "d:\\ue_5.5\\engine\\source\\runtime\\navigationsystem\\public\\navigationdirtyareascontroller.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\math\\movingwindowaveragefast.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\ai\\navigation\\navigationbounds.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\navigationsystem\\uht\\navigationsystem.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\aimodule\\classes\\tasks\\aitask_moveto.h", "d:\\ue_5.5\\engine\\source\\runtime\\aimodule\\classes\\tasks\\aitask.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\aitask.generated.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\aitask_moveto.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\gameframework\\charactermovementcomponent.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\ai\\navigation\\navigationavoidancetypes.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\navigationavoidancetypes.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\public\\ai\\rvoavoidanceinterface.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\rvoavoidanceinterface.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\gameframework\\pawnmovementcomponent.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\pawnmovementcomponent.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\interfaces\\networkpredictioninterface.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\networkpredictioninterface.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\public\\charactermovementcomponentasync.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\charactermovementcomponentasync.generated.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\charactermovementcomponent.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\kismet\\kismetmathlibrary.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\kismetmathlibrary.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\kismet\\kismetmathlibrary.inl", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\kismet\\gameplaystatics.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\kismet\\kismetsystemlibrary.h", "d:\\ue_5.5\\engine\\source\\runtime\\coreuobject\\public\\uobject\\propertyaccessutil.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\kismetsystemlibrary.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\sound\\dialoguetypes.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\dialoguetypes.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\kismet\\gameplaystaticstypes.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\gameplaystaticstypes.generated.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\gameplaystatics.generated.h", "h:\\p4\\dev\\baoli\\source\\baoli\\player\\baoli_character.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\camera\\cameracomponent.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\cameracomponent.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\components\\pointlightcomponent.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\components\\locallightcomponent.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\components\\lightcomponent.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\components\\lightcomponentbase.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\lightcomponentbase.generated.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\lightcomponent.generated.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\locallightcomponent.generated.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\pointlightcomponent.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\gameframework\\springarmcomponent.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\springarmcomponent.generated.h", "d:\\ue_5.5\\engine\\plugins\\enhancedinput\\source\\enhancedinput\\public\\enhancedinputsubsystems.h", "d:\\ue_5.5\\engine\\plugins\\enhancedinput\\source\\enhancedinput\\public\\enhancedinputsubsysteminterface.h", "d:\\ue_5.5\\engine\\plugins\\enhancedinput\\source\\enhancedinput\\public\\enhancedplayerinput.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\gameframework\\playerinput.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\public\\gesturerecognizer.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\public\\keystate.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\playerinput.generated.h", "d:\\ue_5.5\\engine\\plugins\\enhancedinput\\source\\enhancedinput\\public\\inputaction.h", "d:\\ue_5.5\\engine\\plugins\\enhancedinput\\source\\enhancedinput\\public\\inputmodifiers.h", "d:\\ue_5.5\\engine\\plugins\\enhancedinput\\source\\enhancedinput\\public\\inputactionvalue.h", "d:\\ue_5.5\\engine\\plugins\\enhancedinput\\intermediate\\build\\win64\\unrealeditor\\inc\\enhancedinput\\uht\\inputactionvalue.generated.h", "d:\\ue_5.5\\engine\\plugins\\enhancedinput\\intermediate\\build\\win64\\unrealeditor\\inc\\enhancedinput\\uht\\inputmodifiers.generated.h", "d:\\ue_5.5\\engine\\plugins\\enhancedinput\\source\\enhancedinput\\public\\inputtriggers.h", "d:\\ue_5.5\\engine\\plugins\\enhancedinput\\intermediate\\build\\win64\\unrealeditor\\inc\\enhancedinput\\uht\\inputtriggers.generated.h", "d:\\ue_5.5\\engine\\plugins\\enhancedinput\\intermediate\\build\\win64\\unrealeditor\\inc\\enhancedinput\\uht\\inputaction.generated.h", "d:\\ue_5.5\\engine\\plugins\\enhancedinput\\intermediate\\build\\win64\\unrealeditor\\inc\\enhancedinput\\uht\\enhancedplayerinput.generated.h", "d:\\ue_5.5\\engine\\plugins\\enhancedinput\\source\\enhancedinput\\public\\playermappablekeyslot.h", "d:\\ue_5.5\\engine\\plugins\\enhancedinput\\intermediate\\build\\win64\\unrealeditor\\inc\\enhancedinput\\uht\\playermappablekeyslot.generated.h", "d:\\ue_5.5\\engine\\plugins\\enhancedinput\\intermediate\\build\\win64\\unrealeditor\\inc\\enhancedinput\\uht\\enhancedinputsubsysteminterface.generated.h", "d:\\ue_5.5\\engine\\plugins\\enhancedinput\\intermediate\\build\\win64\\unrealeditor\\inc\\enhancedinput\\uht\\enhancedinputsubsystems.generated.h", "d:\\ue_5.5\\engine\\plugins\\animation\\motionwarping\\source\\motionwarping\\public\\motionwarpingcomponent.h", "d:\\ue_5.5\\engine\\plugins\\animation\\motionwarping\\source\\motionwarping\\public\\rootmotionmodifier.h", "d:\\ue_5.5\\engine\\plugins\\animation\\motionwarping\\intermediate\\build\\win64\\unrealeditor\\inc\\motionwarping\\uht\\rootmotionmodifier.generated.h", "d:\\ue_5.5\\engine\\plugins\\animation\\motionwarping\\intermediate\\build\\win64\\unrealeditor\\inc\\motionwarping\\uht\\motionwarpingcomponent.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\components\\spotlightcomponent.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\spotlightcomponent.generated.h", "h:\\p4\\dev\\baoli\\intermediate\\build\\win64\\unrealeditor\\inc\\baoli\\uht\\baoli_character.generated.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}